{"name": "tseer", "private": true, "version": "0.1.0", "description": "Desktop web app with local service & subscription management using Tauri and Rust", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@heroicons/react": "^2.0.18", "@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-dialog": "^2.0.0", "@tauri-apps/plugin-fs": "^2.0.0", "@tauri-apps/plugin-http": "^2.0.0", "@tauri-apps/plugin-shell": "^2.0.0", "axios": "^1.6.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}, "repository": {"type": "git", "url": "git+https://github.com/shogunsea/tseer.git"}, "keywords": ["tauri", "rust", "desktop", "local-service", "subscription", "o<PERSON>h", "typescript", "react"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/shogunsea/tseer/issues"}, "homepage": "https://github.com/shogunsea/tseer#readme"}