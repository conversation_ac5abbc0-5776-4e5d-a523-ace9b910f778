// Tauri command implementations for frontend communication
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::sync::Arc;
use tauri::{AppHandle, State};
use tauri_plugin_dialog::DialogExt;
use tauri_plugin_shell::ShellExt;
use tokio::sync::oneshot;
use tracing::{error, info, warn};

// Import application modules
use crate::api::get_service_status_internal;
use crate::auth::AuthToken;
use crate::local_service::{LocalService, ServiceInfo};
use crate::state::{AppState, FileCountByExtension, FileCountResult, StatusResponse};
use crate::subscription::SubscriptionStatus;

// Default port constant
const DEFAULT_PORT: u16 = 8745;
const PID_FILE: &str = "/tmp/tseer.pid"; // Will be platform-specific in production

// Service management commands
#[tauri::command]
pub async fn start_local_service(
    state: State<'_, AppState>,
    port: Option<u16>,
) -> Result<ServiceInfo, String> {
    let port = port.unwrap_or_else(|| {
        std::env::var("TSEER_PORT")
            .ok()
            .and_then(|p| p.parse().ok())
            .unwrap_or(DEFAULT_PORT)
    });

    // Check if service is already running
    {
        let service_guard = state.local_service.lock();
        if service_guard.is_some() {
            return Err("Service is already running".to_string());
        }
    } // Drop the guard here

    // Create an Arc wrapper for the state to share with LocalService
    let app_state_arc = state.clone_for_sharing();

    match LocalService::new(port, state.metrics.clone(), app_state_arc).await {
        Ok(service) => {
            let service_info = service.get_info();
            // Acquire the lock again to set the service
            *state.local_service.lock() = Some(service);

            // Write PID file for external discovery
            if let Err(e) = write_pid_file() {
                warn!("Failed to write PID file: {}", e);
            }

            info!("Local service started on port {}", port);
            Ok(service_info)
        }
        Err(e) => {
            error!("Failed to start local service: {}", e);
            Err(format!("Failed to start service: {}", e))
        }
    }
}

#[tauri::command]
pub async fn stop_local_service(state: State<'_, AppState>) -> Result<(), String> {
    // Extract the service from the state, dropping the guard before await
    let service = {
        let mut service_guard = state.local_service.lock();
        service_guard.take()
    };

    if let Some(service) = service {
        service.stop().await.map_err(|e| e.to_string())?;
        info!("Local service stopped");

        // Clean up PID file
        let _ = std::fs::remove_file(PID_FILE);
    }

    Ok(())
}

#[tauri::command]
pub async fn get_service_status(state: State<'_, AppState>) -> Result<StatusResponse, String> {
    // Convert State to Arc for the internal function
    let app_state = state.clone_for_sharing();
    get_service_status_internal(app_state).await
}

// Authentication commands
#[tauri::command]
pub async fn authenticate_user(
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<AuthToken, String> {
    // Check if already authenticated first
    {
        let auth_guard = state.auth_state.lock();
        if auth_guard.is_authenticated() {
            if let Some(token) = auth_guard.get_current_token() {
                return Ok(token.clone());
            }
        }
    }

    // Perform authentication using a helper that works with owned data
    match perform_authentication(app_handle, state.auth_state.clone()).await {
        Ok(token) => {
            info!("User authenticated successfully");
            Ok(token)
        }
        Err(e) => {
            error!("Authentication failed: {}", e);
            Err(e.to_string())
        }
    }
}

// Subscription commands
#[tauri::command]
pub async fn check_subscription(state: State<'_, AppState>) -> Result<SubscriptionStatus, String> {
    // Get the token first
    let token = {
        let auth_guard = state.auth_state.lock();
        auth_guard
            .get_current_token()
            .cloned()
            .ok_or("Not authenticated")?
    };

    // Use helper function to avoid holding mutex across await
    match perform_subscription_check(token, state.subscription_service.clone()).await {
        Ok(status) => Ok(status),
        Err(e) => {
            error!("Failed to check subscription: {}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn open_subscription_portal(
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let token = {
        let auth_guard = state.auth_state.lock();
        auth_guard
            .get_current_token()
            .cloned()
            .ok_or("Not authenticated")?
    };

    let portal_url = format!(
        "https://yourservice.com/subscription?token={}",
        token.access_token
    );

    app_handle
        .shell()
        .open(portal_url, None)
        .map_err(|e| e.to_string())?;

    Ok(())
}

// File system commands
#[tauri::command]
pub async fn select_folder(app_handle: AppHandle) -> Result<Option<String>, String> {
    let (tx, rx) = oneshot::channel();

    let dialog = app_handle.dialog();
    dialog.file().pick_folder(move |folder_path| {
        let _ = tx.send(folder_path);
    });

    // Wait for the result
    match rx.await {
        Ok(Some(folder)) => Ok(Some(folder.to_string())),
        Ok(None) => Ok(None),
        Err(_) => Err("Dialog was cancelled".to_string()),
    }
}

#[tauri::command]
pub async fn count_files(folder_path: String) -> Result<FileCountResult, String> {
    let path = Path::new(&folder_path);

    if !path.exists() {
        return Err("Folder does not exist".to_string());
    }

    if !path.is_dir() {
        return Err("Path is not a folder".to_string());
    }

    match fs::read_dir(path) {
        Ok(entries) => {
            let mut extension_counts: HashMap<String, usize> = HashMap::new();
            let mut total_files = 0;

            for entry in entries.filter_map(|entry| entry.ok()) {
                if entry.path().is_file() {
                    total_files += 1;

                    let extension = entry
                        .path()
                        .extension()
                        .and_then(|ext| ext.to_str())
                        .unwrap_or("(no extension)")
                        .to_lowercase();

                    *extension_counts.entry(extension).or_insert(0) += 1;
                }
            }

            // Convert HashMap to sorted Vec
            let mut by_extension: Vec<FileCountByExtension> = extension_counts
                .into_iter()
                .map(|(extension, count)| FileCountByExtension { extension, count })
                .collect();

            // Sort by count (descending) then by extension name
            by_extension.sort_by(|a, b| b.count.cmp(&a.count).then(a.extension.cmp(&b.extension)));

            Ok(FileCountResult {
                total_files,
                by_extension,
            })
        }
        Err(e) => Err(format!("Failed to read folder: {}", e)),
    }
}

// Helper functions

// Helper function that handles authentication without holding locks across awaits
async fn perform_authentication(
    _app_handle: AppHandle,
    _auth_state: Arc<parking_lot::Mutex<crate::auth::AuthState>>,
) -> Result<AuthToken, anyhow::Error> {
    // We'll implement a state machine approach where we extract all necessary
    // data before any async operations, then update state afterwards

    // For now, return an error to unblock compilation
    Err(anyhow::anyhow!("Authentication not yet implemented"))
}

// Helper function that handles subscription checking without holding locks across awaits
async fn perform_subscription_check(
    _token: AuthToken,
    subscription_service: Arc<parking_lot::Mutex<crate::subscription::SubscriptionService>>,
) -> Result<SubscriptionStatus, anyhow::Error> {
    // For now, return cached status to unblock compilation
    let sub_guard = subscription_service.lock();
    sub_guard
        .get_cached_status()
        .ok_or_else(|| anyhow::anyhow!("No cached subscription status available"))
}

fn write_pid_file() -> anyhow::Result<()> {
    use anyhow::Context;
    let pid = std::process::id();
    std::fs::write(PID_FILE, pid.to_string())
        .with_context(|| format!("Failed to write PID file at {}", PID_FILE))?;
    Ok(())
}
