/* cSpell:disable */
// Prevents additional console window on Windows in release builds
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// Error handling and result types
use anyhow::Result;
// Structured logging for application events
use tracing::{error, info};

// Tauri framework core types for app management
use tauri::Manager;

// Module declarations
mod api;
mod auth;
mod commands;
mod config;
mod error;
mod local_service;
mod server;
mod state;
mod subscription;

// Import application modules
use commands::*;
use server::{setup_logging, DEFAULT_PORT};
use state::AppState;

#[tokio::main]
async fn main() -> Result<()> {
    setup_logging();
    info!("Starting TSeer application");

    let app_state = AppState::default();

    tauri::Builder::default()
        .manage(app_state)
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![
            start_local_service,
            stop_local_service,
            get_service_status,
            authenticate_user,
            check_subscription,
            open_subscription_portal,
            select_folder,
            count_files
        ])
        .setup(|app| {
            info!("Tauri app setup complete");

            // Auto-start the local service
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                let state = app_handle.state::<AppState>();
                let port = std::env::var("TSEER_PORT")
                    .ok()
                    .and_then(|p| p.parse().ok())
                    .unwrap_or(DEFAULT_PORT);

                info!("Auto-starting local service on port {}", port);
                match start_local_service(state, Some(port)).await {
                    Ok(service_info) => {
                        info!(
                            "Local service auto-started successfully: {:?}",
                            service_info
                        );
                    }
                    Err(e) => {
                        error!("Failed to auto-start local service: {}", e);
                    }
                }
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");

    Ok(())
}
