// Error handling and context management utilities
use anyhow::{Context, Result};

// JSON serialization/deserialization for service data structures
use serde::{Deserialize, Serialize};
// Network socket address handling for service binding
use std::net::SocketAddr;
// Atomic operations for thread-safe metrics tracking
use std::sync::atomic::{AtomicU32, AtomicU64, Ordering};
// Atomic reference counting for shared state
use std::sync::Arc;
// System time tracking for service uptime and metrics
use std::time::SystemTime;
// TCP listener for HTTP service binding
use tokio::net::TcpListener;
// One-shot channels for service lifecycle coordination
use tokio::sync::oneshot;
// Structured logging for service events
use tracing::info;

// Main application state and REST API factory function
use crate::{api::create_rest_api, state::AppState};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceInfo {
    pub port: u16,
    pub pid: u32,
    pub started: SystemTime,
    pub version: String,
    pub socket_path: Option<String>, // For Unix domain sockets if implemented
}

#[derive(Debug)]
pub struct ServiceMetrics {
    pub start_time: SystemTime,
    pub total_connections: AtomicU64,
    pub active_connections: AtomicU32,
    pub total_requests: AtomicU64,
    pub failed_requests: AtomicU64,
    pub average_response_time: AtomicU64,
}

impl Default for ServiceMetrics {
    fn default() -> Self {
        Self::new()
    }
}

impl ServiceMetrics {
    pub fn new() -> Self {
        Self {
            start_time: SystemTime::now(),
            total_connections: AtomicU64::new(0),
            active_connections: AtomicU32::new(0),
            total_requests: AtomicU64::new(0),
            failed_requests: AtomicU64::new(0),
            average_response_time: AtomicU64::new(0),
        }
    }

    pub fn record_connection(&self) {
        self.total_connections.fetch_add(1, Ordering::Relaxed);
        self.active_connections.fetch_add(1, Ordering::Relaxed);
    }

    pub fn record_disconnection(&self) {
        self.active_connections.fetch_sub(1, Ordering::Relaxed);
    }

    pub fn record_request(&self) {
        self.total_requests.fetch_add(1, Ordering::Relaxed);
    }

    pub fn record_failed_request(&self) {
        self.failed_requests.fetch_add(1, Ordering::Relaxed);
    }

    pub fn export_prometheus(&self) -> String {
        format!(
            "# HELP tseer_connections_total Total number of connections\n\
             # TYPE tseer_connections_total counter\n\
             tseer_connections_total {}\n\
             # HELP tseer_active_connections Active connections\n\
             # TYPE tseer_active_connections gauge\n\
             tseer_active_connections {}\n\
             # HELP tseer_requests_total Total number of requests\n\
             # TYPE tseer_requests_total counter\n\
             tseer_requests_total {}\n\
             # HELP tseer_failed_requests_total Total number of failed requests\n\
             # TYPE tseer_failed_requests_total counter\n\
             tseer_failed_requests_total {}\n",
            self.total_connections.load(Ordering::Relaxed),
            self.active_connections.load(Ordering::Relaxed),
            self.total_requests.load(Ordering::Relaxed),
            self.failed_requests.load(Ordering::Relaxed)
        )
    }
}

pub struct LocalService {
    info: ServiceInfo,
    shutdown_tx: Option<oneshot::Sender<()>>,
    metrics: Arc<ServiceMetrics>,
}

impl LocalService {
    pub async fn new(
        port: u16,
        metrics: Arc<ServiceMetrics>,
        app_state: Arc<AppState>,
    ) -> Result<Self> {
        // Try to bind to the specified port
        let addr: SocketAddr = format!("127.0.0.1:{}", port).parse()?;
        let listener = TcpListener::bind(&addr).await.with_context(|| {
            format!("Port {} already in use. Is another instance running?", port)
        })?;

        let actual_port = listener.local_addr()?.port();

        info!("Local service binding to http://localhost:{}", actual_port);
        info!("Test with: curl http://localhost:{}/health", actual_port);

        let service_info = ServiceInfo {
            port: actual_port,
            pid: std::process::id(),
            started: SystemTime::now(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            socket_path: None,
        };

        // Write service registry file
        Self::write_service_registry(&service_info)?;

        let (shutdown_tx, shutdown_rx) = oneshot::channel();

        // Start the server in the background
        let _server_metrics = metrics.clone();
        tokio::spawn(async move {
            let api = create_rest_api(app_state).await;

            let (_addr, server) = warp::serve(api).bind_with_graceful_shutdown(addr, async {
                shutdown_rx.await.ok();
            });

            info!(
                "REST API server started on http://localhost:{}",
                actual_port
            );
            server.await;
            info!("REST API server stopped");
        });

        Ok(Self {
            info: service_info,
            shutdown_tx: Some(shutdown_tx),
            metrics,
        })
    }

    pub fn get_info(&self) -> ServiceInfo {
        self.info.clone()
    }

    pub async fn stop(mut self) -> Result<()> {
        if let Some(tx) = self.shutdown_tx.take() {
            let _ = tx.send(());
            info!("Shutdown signal sent to local service");
        }

        // Clean up service registry
        Self::cleanup_service_registry()?;

        Ok(())
    }

    fn write_service_registry(info: &ServiceInfo) -> Result<()> {
        let registry_path = Self::get_registry_path()?;

        // Ensure parent directory exists
        if let Some(parent) = registry_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let json =
            serde_json::to_string_pretty(info).context("Failed to serialize service info")?;

        std::fs::write(&registry_path, json)
            .with_context(|| format!("Failed to write service registry to {:?}", registry_path))?;

        info!("Service registry written to {:?}", registry_path);
        Ok(())
    }

    fn cleanup_service_registry() -> Result<()> {
        let registry_path = Self::get_registry_path()?;

        if registry_path.exists() {
            std::fs::remove_file(&registry_path).with_context(|| {
                format!("Failed to remove service registry at {:?}", registry_path)
            })?;
            info!("Service registry cleaned up");
        }

        Ok(())
    }

    fn get_registry_path() -> Result<std::path::PathBuf> {
        // Platform-specific registry locations
        #[cfg(target_os = "windows")]
        let path = std::env::temp_dir().join("tseer.json");

        #[cfg(target_os = "macos")]
        let path = dirs::config_dir()
            .unwrap_or_else(|| std::env::temp_dir())
            .join("tseer")
            .join("service.json");

        #[cfg(target_os = "linux")]
        let path = std::path::Path::new("/tmp/tseer.json").to_path_buf();

        Ok(path)
    }

    pub fn read_service_registry() -> Result<ServiceInfo> {
        let registry_path = Self::get_registry_path()?;

        let data = std::fs::read_to_string(&registry_path)
            .with_context(|| format!("Failed to read service registry from {:?}", registry_path))?;

        let info: ServiceInfo =
            serde_json::from_str(&data).context("Failed to parse service registry")?;

        Ok(info)
    }
}

impl Drop for LocalService {
    fn drop(&mut self) {
        // Clean up on drop
        let _ = Self::cleanup_service_registry();
        if let Some(tx) = self.shutdown_tx.take() {
            let _ = tx.send(());
        }
    }
}

// Service management utilities for CLI usage
pub struct ServiceManager;

impl ServiceManager {
    pub fn is_service_running() -> bool {
        // Check if service is running by trying to connect
        if let Ok(info) = LocalService::read_service_registry() {
            // Try to make a health check request
            let client = reqwest::Client::new();
            let url = format!("http://localhost:{}/health", info.port);

            tokio::runtime::Runtime::new()
                .map(|rt| rt.block_on(async { client.get(&url).send().await.is_ok() }))
                .unwrap_or(false)
        } else {
            false
        }
    }

    pub fn get_service_port() -> Option<u16> {
        LocalService::read_service_registry()
            .ok()
            .map(|info| info.port)
    }

    pub fn get_service_info() -> Option<ServiceInfo> {
        LocalService::read_service_registry().ok()
    }

    pub async fn health_check(port: Option<u16>) -> Result<bool> {
        let port = port.unwrap_or(crate::DEFAULT_PORT);
        let client = reqwest::Client::new();
        let url = format!("http://localhost:{}/health", port);

        match client.get(&url).send().await {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => Ok(false),
        }
    }
}
