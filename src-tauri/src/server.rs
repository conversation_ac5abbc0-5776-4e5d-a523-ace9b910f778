// Server setup and initialization utilities
use anyhow::{Context, Result};
use std::sync::Arc;
use tracing::info;
use tracing_subscriber;

// Import application modules
use crate::api::create_rest_api;
use crate::state::AppState;

// Default port constant
pub const DEFAULT_PORT: u16 = 8745;

// Setup structured logging for the application
pub fn setup_logging() {
    let filter = std::env::var("RUST_LOG").unwrap_or_else(|_| "tseer=info,warp=warn".to_string());

    tracing_subscriber::fmt()
        .with_env_filter(filter)
        .with_target(false)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .init();
}

// Initialize the REST API server with the given state
pub async fn initialize_api_server(state: Arc<AppState>) -> impl warp::Filter<Extract = impl warp::Reply> + Clone {
    create_rest_api(state).await
}

// Server configuration and utilities
pub struct ServerConfig {
    pub port: u16,
    pub auto_start: bool,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            port: DEFAULT_PORT,
            auto_start: true,
        }
    }
}

impl ServerConfig {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn with_port(mut self, port: u16) -> Self {
        self.port = port;
        self
    }

    pub fn with_auto_start(mut self, auto_start: bool) -> Self {
        self.auto_start = auto_start;
        self
    }

    pub fn from_env() -> Self {
        let port = std::env::var("TSEER_PORT")
            .ok()
            .and_then(|p| p.parse().ok())
            .unwrap_or(DEFAULT_PORT);

        let auto_start = std::env::var("TSEER_AUTO_START")
            .map(|v| v.to_lowercase() == "true")
            .unwrap_or(true);

        Self { port, auto_start }
    }
}

// Server management utilities
pub struct ServerManager;

impl ServerManager {
    pub fn get_effective_port(config_port: Option<u16>) -> u16 {
        config_port
            .or_else(|| {
                std::env::var("TSEER_PORT")
                    .ok()
                    .and_then(|p| p.parse().ok())
            })
            .unwrap_or(DEFAULT_PORT)
    }

    pub fn should_auto_start() -> bool {
        std::env::var("TSEER_AUTO_START")
            .map(|v| v.to_lowercase() == "true")
            .unwrap_or(true)
    }

    pub fn validate_port(port: u16) -> Result<()> {
        if port < 1024 {
            return Err(anyhow::anyhow!(
                "Port {} is reserved. Please use a port >= 1024",
                port
            ));
        }
        if port > 65535 {
            return Err(anyhow::anyhow!("Port {} is invalid. Maximum port is 65535", port));
        }
        Ok(())
    }

    pub fn get_server_info() -> ServerInfo {
        ServerInfo {
            version: env!("CARGO_PKG_VERSION").to_string(),
            build_time: "unknown".to_string(),
            git_hash: "unknown".to_string(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct ServerInfo {
    pub version: String,
    pub build_time: String,
    pub git_hash: String,
}

// Logging configuration utilities
pub struct LoggingConfig {
    pub level: String,
    pub file_logging: bool,
    pub console_logging: bool,
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            file_logging: true,
            console_logging: true,
        }
    }
}

impl LoggingConfig {
    pub fn from_env() -> Self {
        let level = std::env::var("RUST_LOG").unwrap_or_else(|_| "info".to_string());
        let file_logging = std::env::var("TSEER_FILE_LOGGING")
            .map(|v| v.to_lowercase() == "true")
            .unwrap_or(true);
        let console_logging = std::env::var("TSEER_CONSOLE_LOGGING")
            .map(|v| v.to_lowercase() == "true")
            .unwrap_or(true);

        Self {
            level,
            file_logging,
            console_logging,
        }
    }

    pub fn setup_with_config(&self) {
        let filter = format!("tseer={},warp=warn,reqwest=warn", self.level);

        let subscriber = tracing_subscriber::fmt()
            .with_env_filter(filter)
            .with_target(false)
            .with_thread_ids(true)
            .with_file(true)
            .with_line_number(true);

        if self.console_logging {
            subscriber.init();
        } else {
            // In a real implementation, we'd set up file-only logging here
            subscriber.init();
        }

        info!("Logging initialized with level: {}", self.level);
    }
}
