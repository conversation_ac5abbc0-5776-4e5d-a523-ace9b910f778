// Application state management and shared data structures
use parking_lot::Mutex;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

// Import modules for state components
use crate::auth::AuthState;
use crate::config::AppConfig;
use crate::local_service::{LocalService, ServiceMetrics};
use crate::subscription::SubscriptionService;

// Main application state shared across the entire application
#[derive(Default)]
pub struct AppState {
    pub local_service: Arc<Mutex<Option<LocalService>>>,
    pub auth_state: Arc<Mutex<AuthState>>,
    pub subscription_service: Arc<Mutex<SubscriptionService>>,
    pub config: Arc<Mutex<AppConfig>>,
    pub metrics: Arc<ServiceMetrics>,
}

// API request/response types for REST endpoints
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiRequest {
    pub method: String,
    pub params: serde_json::Value,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse {
    pub success: bool,
    pub result: Option<serde_json::Value>,
    pub error: Option<String>,
}

// Health check response structure
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub port: u16,
    pub pid: u32,
    pub version: String,
    pub uptime_seconds: u64,
}

// Detailed status response structure
#[derive(Debug, Serialize, Deserialize)]
pub struct StatusResponse {
    pub port: u16,
    pub pid: u32,
    pub version: String,
    pub uptime_seconds: u64,
    pub service_info: crate::local_service::ServiceInfo,
    pub subscription_status: Option<crate::subscription::SubscriptionStatus>,
    pub auth_status: bool,
}

// File counting utility types
#[derive(Debug, Serialize, Deserialize)]
pub struct FileCountByExtension {
    pub extension: String,
    pub count: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileCountResult {
    pub total_files: usize,
    pub by_extension: Vec<FileCountByExtension>,
}

impl AppState {
    pub fn new() -> Self {
        Self::default()
    }

    // Helper method to create an Arc-wrapped version of the state for sharing
    pub fn as_arc(self) -> Arc<Self> {
        Arc::new(self)
    }

    // Helper method to clone the state for sharing across async boundaries
    pub fn clone_for_sharing(&self) -> Arc<AppState> {
        Arc::new(AppState {
            local_service: self.local_service.clone(),
            auth_state: self.auth_state.clone(),
            subscription_service: self.subscription_service.clone(),
            config: self.config.clone(),
            metrics: self.metrics.clone(),
        })
    }
}
