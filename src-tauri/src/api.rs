// REST API handlers and endpoint implementations
use chrono::Utc;
use std::sync::Arc;
use warp::{Filter, Rejection, Reply};

// Import application state and response types
use crate::state::{ApiRequest, ApiResponse, AppState, HealthResponse, StatusResponse};

// Create the complete REST API with all routes
pub async fn create_rest_api(state: Arc<AppState>) -> impl Filter<Extract = impl Reply> + Clone {
    let state_filter = warp::any().map(move || state.clone());

    // Health check endpoint - no auth required
    let health = warp::path("health")
        .and(warp::get())
        .and(state_filter.clone())
        .and_then(handle_health);

    // Status endpoint for debugging
    let status = warp::path("status")
        .and(warp::get())
        .and(state_filter.clone())
        .and_then(handle_status);

    // Main API endpoints
    let api = warp::path("api")
        .and(warp::path("v1"))
        .and(warp::post())
        .and(warp::header::optional::<String>("authorization"))
        .and(warp::body::json())
        .and(state_filter.clone())
        .and_then(handle_api_request);

    // CORS headers for local development
    let cors = warp::cors()
        .allow_any_origin()
        .allow_headers(vec!["authorization", "content-type"])
        .allow_methods(vec!["GET", "POST", "OPTIONS"]);

    health.or(status).or(api).with(cors).with(warp::log("api"))
}

// Health check handler
pub async fn handle_health(state: Arc<AppState>) -> Result<impl Reply, Rejection> {
    let service_guard = state.local_service.lock();

    if let Some(service) = service_guard.as_ref() {
        let service_info = service.get_info();
        let response = HealthResponse {
            status: "healthy".to_string(),
            port: service_info.port,
            pid: service_info.pid,
            version: service_info.version.clone(),
            uptime_seconds: service_info.started.elapsed().unwrap_or_default().as_secs(),
        };
        Ok(warp::reply::json(&response))
    } else {
        Ok(warp::reply::json(&serde_json::json!({
            "status": "service_not_running"
        })))
    }
}

// Status endpoint handler
pub async fn handle_status(state: Arc<AppState>) -> Result<impl Reply, Rejection> {
    match get_service_status_internal(state.clone()).await {
        Ok(status) => Ok(warp::reply::json(&status)),
        Err(e) => Ok(warp::reply::json(&serde_json::json!({
            "error": e
        }))),
    }
}

// Main API request handler
pub async fn handle_api_request(
    auth_header: Option<String>,
    request: ApiRequest,
    _state: Arc<AppState>,
) -> Result<impl Reply, Rejection> {
    // Simple token validation for CLI clients
    if let Some(auth) = auth_header {
        if !auth.starts_with("Bearer ") {
            return Ok(warp::reply::with_status(
                warp::reply::json(&ApiResponse {
                    success: false,
                    result: None,
                    error: Some("Invalid authorization format".to_string()),
                }),
                warp::http::StatusCode::UNAUTHORIZED,
            ));
        }
    }

    let response = match request.method.as_str() {
        "process" => {
            // Example API method for processing data
            let result = serde_json::json!({
                "output": format!("Processed: {:?}", request.params),
                "timestamp": Utc::now()
            });

            ApiResponse {
                success: true,
                result: Some(result),
                error: None,
            }
        }
        "get_data" => {
            // Example API method for getting data
            let result = serde_json::json!({
                "data": "example_data",
                "timestamp": Utc::now()
            });

            ApiResponse {
                success: true,
                result: Some(result),
                error: None,
            }
        }
        _ => ApiResponse {
            success: false,
            result: None,
            error: Some("Unknown method".to_string()),
        },
    };

    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        if response.success {
            warp::http::StatusCode::OK
        } else {
            warp::http::StatusCode::BAD_REQUEST
        },
    ))
}

// Internal helper function for getting service status
pub async fn get_service_status_internal(state: Arc<AppState>) -> Result<StatusResponse, String> {
    let service_guard = state.local_service.lock();
    let auth_guard = state.auth_state.lock();
    let sub_guard = state.subscription_service.lock();

    if let Some(service) = service_guard.as_ref() {
        let service_info = service.get_info();
        let subscription_status = sub_guard.get_cached_status();

        Ok(StatusResponse {
            port: service_info.port,
            pid: service_info.pid,
            version: service_info.version.clone(),
            uptime_seconds: service_info.started.elapsed().unwrap_or_default().as_secs(),
            service_info,
            subscription_status,
            auth_status: auth_guard.is_authenticated(),
        })
    } else {
        Err("Service not running".to_string())
    }
}
